const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('DriverInvoices', {
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    DriverID: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Driver',
        key: 'DriverID'
      }
    },
    Date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    Price: {
      type: DataTypes.DECIMAL(10,2),
      allowNull: true
    },
    ReceivedBy: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    DriverProfit: {
      type: DataTypes.DECIMAL(10,2),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'DriverInvoices',
    schema: 'dbo',
    timestamps: false,
    indexes: [
      {
        name: "PK__DriverIn__3213E83FC73EF6CB",
        unique: true,
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
};
