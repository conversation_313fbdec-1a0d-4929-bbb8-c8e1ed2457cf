const { errorResponse } = require('../utils/responseFormatter');

/**
 * Middleware to check driver permissions
 */
const checkDriverPermission = (req, res, next) => {
    // Skip permission check for public routes
    if (req.originalUrl.startsWith('/driver-auth')) {
        return next();
    }
    
    // Verify that the user is a driver
    if (!req.driver || req.driver.type !== 'driver') {
        const { response, statusCode } = errorResponse(
            'غير مصرح للدخول - يجب أن تكون سائقًا', 
            403, 
            'FORBIDDEN'
        );
        return res.status(statusCode).json(response);
    }
    
    // You can add specific route permissions here if needed
    // Example:
    // const path = req.path;
    // if (path.startsWith('/some-protected-route') && !userHasAccess) {
    //     return res.status(403).json({
    //         success: false,
    //         message: 'غير مسموح لك بالوصول إلى هذه الصفحة'
    //     });
    // }

    next();
};

module.exports = checkDriverPermission; 