const jwt = require('jsonwebtoken');
const { errorResponse } = require('../utils/responseFormatter');

/**
 * Middleware to authenticate driver tokens
 */
const authenticateDriverToken = (req, res, next) => {
    
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        const { response, statusCode } = errorResponse(
            'يجب تسجيل الدخول للوصول', 
            401, 
            'UNAUTHORIZED'
        );
        return res.status(statusCode).json(response);
    }

    jwt.verify(token, process.env.JWT_DRIVER_SECRET, (err, decoded) => {
        if (err) {
            let errorMessage = 'جلسة غير صالحة';
            let errorCode = 'INVALID_TOKEN';

            if (err.name === 'TokenExpiredError') {
                errorMessage = 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى';
                errorCode = 'TOKEN_EXPIRED';
            }

            const { response, statusCode } = errorResponse(
                errorMessage, 
                401, 
                errorCode
            );
            return res.status(statusCode).json(response);
        }

        if (decoded.type !== 'driver') {
            const { response, statusCode } = errorResponse(
                'نوع المستخدم غير مسموح به', 
                403, 
                'FORBIDDEN'
            );
            return res.status(statusCode).json(response);
        }

        req.driver = {
            id: decoded.driverId,
            type: decoded.type
        };
        
        next();
    });
};

module.exports = {
    authenticateDriverToken
}; 