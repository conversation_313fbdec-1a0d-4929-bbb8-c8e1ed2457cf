const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Restaurant', {
    RestaurantID: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    Name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    Image: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    City: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    Address: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    Status: {
      type: DataTypes.TINYINT,
      allowNull: true,
      defaultValue: 1
    },
    InsertBy: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    UpdateBy: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    InsertDate: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: Sequelize.Sequelize.fn('getdate')
    },
    UpdateDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    Stats: {
      type: DataTypes.TINYINT,
      allowNull: true
    },
    Money: {
      type: DataTypes.DECIMAL(10,2),
      allowNull: true
    },
    longitude: {
      type: DataTypes.DECIMAL(9,6),
      allowNull: true
    },
    latitude: {
      type: DataTypes.DECIMAL(8,6),
      allowNull: true
    },
    RestaurantTypeID: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'RestaurantType',
        key: 'RestaurantTypeID'
      }
    },
    Activated: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    Owner: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    firstNumber: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    secondNumber: {
      type: DataTypes.STRING(100),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'Restaurant',
    schema: 'dbo',
    timestamps: false,
    indexes: [
      {
        name: "PK__Restaura__87454CB57271D331",
        unique: true,
        fields: [
          { name: "RestaurantID" },
        ]
      },
    ]
  });
};
