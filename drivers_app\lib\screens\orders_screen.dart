import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:geolocator/geolocator.dart';
import '../providers/auth_provider.dart';
import '../screens/order_completion_screen.dart';
import '../screens/delivery_screen.dart';
import '../services/api_client.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> with TickerProviderStateMixin {
  bool _isLoading = false;
  String? _errorMessage;
  List<dynamic> _availableOrders = [];
  Map<String, dynamic>? _currentOrder;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  Position? _driverPosition;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _checkForActiveOrder();
    _getCurrentLocation();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Get current driver location
  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) return;

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) return;
      }

      if (permission == LocationPermission.deniedForever) return;

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      setState(() {
        _driverPosition = position;
      });
    } catch (e) {
      // Silently handle location errors
    }
  }

  // Calculate distance between two points
  double _calculateDistance(double lat1, double lng1, double lat2, double lng2) {
    return Geolocator.distanceBetween(lat1, lng1, lat2, lng2) / 1000; // Convert to kilometers
  }

  Future<void> _checkForActiveOrder() async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.get('/drivers/app/active-order');

      if (response != null && response['hasActiveOrder'] == true) {
        // Driver has an active order, navigate to delivery screen
        final activeOrder = response['data'];
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => DeliveryScreen(order: activeOrder),
            ),
          ).then((_) {
            // Refresh orders when returning from delivery screen
            _loadOrders();
          });
        }
      } else {
        // No active order, load available orders
        _loadOrders();
      }
    } catch (e) {
      // If checking for active order fails, just load orders normally
      _loadOrders();
    }
  }

  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      if (authProvider.currentDriver == null) {
        throw Exception("معلومات السائق غير كاملة");
      }

      // Fetch available orders and current order
      await Future.wait([
        _fetchAvailableOrders(),
        _fetchCurrentOrder(),
      ]);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = "فشل في تحميل الطلبات: $e";
        _isLoading = false;
      });
    }
  }

  Future<void> _fetchAvailableOrders() async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.get('/drivers/app/available-orders?page=1&limit=50');

      print('Available orders response: $response');

      if (response != null && response['items'] != null) {
        final orders = List<dynamic>.from(response['items']);
        print('First order data: ${orders.isNotEmpty ? orders.first : 'No orders'}');
        setState(() {
          _availableOrders = orders;
        });
      } else {
        setState(() {
          _availableOrders = [];
        });
      }
    } catch (e) {
      print('Error fetching available orders: $e');
      setState(() {
        _availableOrders = [];
      });
    }
  }

  Future<void> _fetchCurrentOrder() async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.get('/drivers/app/my-orders?page=1&limit=1');

      if (response != null && response['items'] != null) {
        final orders = List<dynamic>.from(response['items']);
        // Find current order (Status = 2 - Out for delivery)
        final currentOrders = orders.where((order) => order['Status'] == 2).toList();
        setState(() {
          _currentOrder = currentOrders.isNotEmpty ? currentOrders.first : null;
        });
      }
    } catch (e) {
      setState(() {
        _currentOrder = null;
      });
    }
  }

  Future<void> _acceptOrder(Map<String, dynamic> order) async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.post('/drivers/app/accept-order/${order['OrderID']}', {});

      if (response['success']) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم قبول الطلب #${order['OrderID']} بنجاح'),
              backgroundColor: Colors.green,
            ),
          );

          // Navigate to delivery screen
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => DeliveryScreen(
                order: {
                  ...order,
                  'Status': 2, // Update status to "Out for Delivery"
                  'Claimed': true,
                },
              ),
            ),
          ).then((_) {
            // Refresh orders when returning from delivery screen
            _loadOrders();
          });
        }
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'فشل في قبول الطلب';

        // Check if it's an active order error
        if (e.toString().contains('لديك طلب نشط بالفعل')) {
          errorMessage = 'لديك طلب نشط بالفعل. يجب إنهاء الطلب الحالي أولاً';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.purple[50]!,
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.purple[400]!, Colors.purple[600]!],
                        ),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Icon(
                        Icons.assignment,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إدارة الطلبات',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          Text(
                            'اقبل الطلبات وتابع حالتها',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: _loadOrders,
                      icon: Icon(
                        Icons.refresh,
                        color: Colors.purple[600],
                      ),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.purple[50],
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Current Order Card (if exists)
              if (_currentOrder != null) ...[
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue[600]!, Colors.blue[800]!],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withValues(alpha: 0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.delivery_dining,
                            color: Colors.white,
                            size: 28,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'لديك طلب قيد التوصيل',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  'طلب #${_currentOrder!['OrderID']}',
                                  style: TextStyle(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          ElevatedButton(
                            onPressed: () => _navigateToOrderCompletion(_currentOrder!),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: Colors.blue[600],
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'متابعة',
                              style: TextStyle(fontWeight: FontWeight.w600),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
              ],

              // Available Orders Section
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Icon(
                      Icons.search,
                      color: Colors.purple[600],
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'الطلبات المتاحة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Content
              Expanded(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildAvailableOrdersList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvailableOrdersList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.purple),
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadOrders,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple[600],
                foregroundColor: Colors.white,
              ),
              child: const Text("إعادة المحاولة"),
            ),
          ],
        ),
      );
    }

    if (_availableOrders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.purple[50],
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                Icons.search_off,
                size: 64,
                color: Colors.purple[400],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              "لا توجد طلبات متاحة حالياً",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              "سيتم إشعارك عند توفر طلبات جديدة",
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadOrders,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple[600],
                foregroundColor: Colors.white,
              ),
              child: const Text("تحديث"),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadOrders,
      color: Colors.purple[600],
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: _availableOrders.length,
        itemBuilder: (context, index) {
          return _buildOrderCard(_availableOrders[index], true);
        },
      ),
    );
  }

  void _navigateToOrderCompletion(Map<String, dynamic> order) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OrderCompletionScreen(order: order),
      ),
    );

    // If order was completed, refresh the orders
    if (result == true) {
      _loadOrders();
    }
  }

  Widget _buildOrderCard(Map<String, dynamic> order, bool isAvailable) {
    final orderId = order['OrderID']?.toString() ?? 'N/A';
    final restaurantName = order['RestaurantName'] ?? 'مطعم';
    final total = order['CartTotalPrice']?.toDouble() ?? 0.0;
    final status = order['Status'] ?? 0;

    final estimatedProfit = order['EstimatedDriverProfit']?.toDouble() ?? 0.0;
    final deliveryFee = order['TotalDeliveryFee']?.toDouble() ?? 0.0;
    final totalPayment = order['TotalCustomerPayment']?.toDouble() ?? 0.0;

    // Calculate distances if driver position is available
    double? restaurantDistance;
    double? customerDistance;

    if (_driverPosition != null) {
      if (order['StartPointLatitude'] != null && order['StartPointLongitude'] != null) {
        restaurantDistance = _calculateDistance(
          _driverPosition!.latitude,
          _driverPosition!.longitude,
          order['StartPointLatitude'].toDouble(),
          order['StartPointLongitude'].toDouble()
        );
      }
      if (order['EndPointLatitude'] != null && order['EndPointLongitude'] != null) {
        customerDistance = _calculateDistance(
          _driverPosition!.latitude,
          _driverPosition!.longitude,
          order['EndPointLatitude'].toDouble(),
          order['EndPointLongitude'].toDouble()
        );
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'طلب #$orderId',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                if (!isAvailable) _buildStatusBadge(status),
              ],
            ),

            const SizedBox(height: 12),

            // Restaurant name
            Text(
              restaurantName,
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),

            const SizedBox(height: 12),

            // Distance info
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المسافة للمطعم',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        restaurantDistance != null
                            ? '${restaurantDistance.toStringAsFixed(1)} كم'
                            : '-- كم',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مسافة التوصيل',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        customerDistance != null
                            ? '${customerDistance.toStringAsFixed(1)} كم'
                            : '-- كم',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'إجمالي المسافة',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      restaurantDistance != null && customerDistance != null
                          ? '${(restaurantDistance + customerDistance).toStringAsFixed(1)} كم'
                          : '-- كم',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Financial info
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'قيمة الطلب',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        '${total.toStringAsFixed(2)} د.ل',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
                if (deliveryFee > 0)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'رسوم التوصيل',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          '${deliveryFee.toStringAsFixed(2)} د.ل',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                if (estimatedProfit > 0)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'ربحك',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.purple[600],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '${estimatedProfit.toStringAsFixed(2)} د.ل',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),

            if (isAvailable) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => _acceptOrder(order),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'قبول الطلب',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ] else ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'طلب مُعيَّن لسائق آخر',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(int status) {
    String text;
    Color color;

    switch (status) {
      case 0:
        text = 'في الانتظار';
        color = Colors.orange;
        break;
      case 1:
        text = 'بانتظار الاستلام';
        color = Colors.blue;
        break;
      case 2:
        text = 'في الطريق';
        color = Colors.purple;
        break;
      case 3:
        text = 'تم التسليم';
        color = Colors.green;
        break;
      default:
        text = 'غير معروف';
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
