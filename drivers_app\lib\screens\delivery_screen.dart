import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:geolocator/geolocator.dart';
import '../services/api_client.dart';

class DeliveryScreen extends StatefulWidget {
  final Map<String, dynamic> order;

  const DeliveryScreen({Key? key, required this.order}) : super(key: key);

  @override
  _DeliveryScreenState createState() => _DeliveryScreenState();
}

class _DeliveryScreenState extends State<DeliveryScreen> {
  bool _isLoading = false;
  late Map<String, dynamic> _currentOrder;
  MapController? _mapController;
  int _currentStage = 1; // 1: Going to Restaurant, 2: Delivering to Customer

  // Coordinates
  double? _restaurantLat;
  double? _restaurantLng;
  double? _customerLat;
  double? _customerLng;

  // Driver location
  Position? _driverPosition;
  bool _locationPermissionGranted = false;

  @override
  void initState() {
    super.initState();
    _currentOrder = widget.order;
    _mapController = MapController();
    _extractCoordinates();
    _checkLocationPermission();
  }

  void _extractCoordinates() {
    print('Full order data: $_currentOrder');

    _restaurantLat = _currentOrder['StartPointLatitude']?.toDouble();
    _restaurantLng = _currentOrder['StartPointLongitude']?.toDouble();

    _customerLat = _currentOrder['EndPointLatitude']?.toDouble();
    _customerLng = _currentOrder['EndPointLongitude']?.toDouble();

    print('Extracted coordinates:');
    print('Restaurant: $_restaurantLat, $_restaurantLng');
    print('Customer: $_customerLat, $_customerLng');
    print('Raw coordinates from order:');
    print('StartPointLatitude: ${_currentOrder['StartPointLatitude']}');
    print('StartPointLongitude: ${_currentOrder['StartPointLongitude']}');
    print('EndPointLatitude: ${_currentOrder['EndPointLatitude']}');
    print('EndPointLongitude: ${_currentOrder['EndPointLongitude']}');
  }

  Future<void> _checkLocationPermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      _showLocationServiceDialog();
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        _showPermissionDeniedDialog();
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      _showPermissionDeniedForeverDialog();
      return;
    }

    setState(() {
      _locationPermissionGranted = true;
    });

    _getCurrentLocation();
  }

  // Get current driver location
  Future<void> _getCurrentLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      setState(() {
        _driverPosition = position;
      });
    } catch (e) {
      print('Error getting location: $e');
    }
  }

  // Navigate to Google Maps
  Future<void> _navigateToLocation(double lat, double lng, String destination) async {
    final url = 'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }

  // Launch phone dialer with number
  Future<void> _launchPhoneApp(String phoneNumber) async {
    if (phoneNumber.isNotEmpty) {
      final url = 'tel:$phoneNumber';
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url));
      } else {
        // Optionally, show an error to the user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تعذر فتح تطبيق الهاتف')),
          );
        }
      }
    }
  }

  // Calculate distance between two points
  double _calculateDistance(double lat1, double lng1, double lat2, double lng2) {
    return Geolocator.distanceBetween(lat1, lng1, lat2, lng2) / 1000; // Convert to kilometers
  }

  // Show location service dialog
  void _showLocationServiceDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('خدمة الموقع مطلوبة'),
        content: const Text('يرجى تفعيل خدمة الموقع من إعدادات الجهاز للمتابعة.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await Geolocator.openLocationSettings();
            },
            child: const Text('فتح الإعدادات'),
          ),
        ],
      ),
    );
  }

  // Show permission denied dialog
  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('إذن الموقع مطلوب'),
        content: const Text('يرجى السماح للتطبيق بالوصول إلى موقعك لحساب المسافات وتحسين تجربة التوصيل.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _checkLocationPermission();
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  // Show permission denied forever dialog
  void _showPermissionDeniedForeverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('إذن الموقع مرفوض'),
        content: const Text('تم رفض إذن الموقع نهائياً. يرجى فتح إعدادات التطبيق والسماح بالوصول إلى الموقع.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await Geolocator.openAppSettings();
            },
            child: const Text('فتح إعدادات التطبيق'),
          ),
        ],
      ),
    );
  }

  // Move to next stage
  void _moveToNextStage() {
    setState(() {
      _currentStage = 2;
    });
  }

  // Complete delivery
  Future<void> _completeDelivery() async {
    await _updateOrderStatus(3);
  }

  // Report delivery issue
  Future<void> _reportIssue(int status, String reason) async {
    await _updateOrderStatus(status);
  }

  Future<void> _updateOrderStatus(int status) async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final apiClient = ApiClient();
      final response = await apiClient.put(
        '/drivers/app/delivery-status/${_currentOrder['OrderID']}',
        {'status': status},
      );

      if (response['success'] && mounted) {
        setState(() {
          _currentOrder['Status'] = status;
        });

        String message = '';
        switch (status) {
          case 3:
            message = 'تم تسليم الطلب بنجاح';
            Navigator.of(context).popUntil((route) => route.isFirst);
            break;
          case 5:
            message = 'تم تسجيل عدم استلام العميل للطلب';
            Navigator.of(context).popUntil((route) => route.isFirst);
            break;
          case 7:
            message = 'تم إنهاء الرحلة بسبب فشل التوصيل';
            Navigator.of(context).popUntil((route) => route.isFirst);
            break;
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('حدث خطأ أثناء تحديث حالة الطلب'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

@override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    final orderId = _currentOrder['OrderID']?.toString() ?? 'N/A';
    final restaurantName = _currentOrder['RestaurantName'] ?? 'مطعم';
    final total = _currentOrder['CartTotalPrice']?.toDouble() ?? 0.0;
    final customerPhone = _currentOrder['Customer']?['PhoneNum'] ?? '';

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
appBar: AppBar(
  backgroundColor: Colors.purple[600],
  elevation: 0,
  iconTheme: const IconThemeData(color: Colors.white),
  title: RichText(
    text: TextSpan(
      style: const TextStyle(fontFamily: 'Alx'), // استخدام خط Alx
      children: [
        const TextSpan(
          text: 'توصيل الطلب ',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        TextSpan(
          text: '#$orderId',
          style: TextStyle(
            fontFamily: 'Alx',
            color: Colors.amberAccent.shade200,
            fontSize: 26,
            fontWeight: FontWeight.w900,
            letterSpacing: 1.5,
            shadows: const [
              Shadow(
                color: Colors.black54,
                offset: Offset(1, 1),
                blurRadius: 3,
              ),
              Shadow(
                color: Colors.amber,
                offset: Offset(0, 0),
                blurRadius: 8,
              ),
            ],
          ),
        ),
      ],
    ),
  ),
  actions: [
    PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert, color: Colors.white),
      onSelected: _handleMenuSelection,
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'report',
          child: Text('بلاغ'),
        ),
      ],
    ),
  ],
),

      body: Column(
        children: [
          // Map Section
          _buildMapSection(),

          // Order Info Card
          _buildOrderInfoCard(orderId, restaurantName, total, customerPhone),

          // Stage Progress
          _buildStageProgress(),

          // Action Buttons
          Expanded(
            child: _buildActionButtons(),
          ),
        ],
      ),
    );
  }

  void _handleMenuSelection(String value) {
    if (value == 'report') {
      _showReportMenu();
    }
  }

  void _showReportMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'بلاغ',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.cancel, color: Colors.red),
              title: const Text('إنهاء الرحلة بسبب الفشل في التوصيل'),
              onTap: () {
                Navigator.pop(context);
                _showWarningModal(7, 'فشل التوصيل');
              },
            ),
            ListTile(
              leading: const Icon(Icons.person_off, color: Colors.orange),
              title: const Text('إنهاء الرحلة بسبب عدم استلام الزبون'),
              onTap: () {
                Navigator.pop(context);
                _showWarningModal(5, 'عدم استلام الزبون');
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // Warning modal for reports
void _showWarningModal(int status, String reason) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
      elevation: 20,
      backgroundColor: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(22),
        child: Directionality(
          textDirection: TextDirection.rtl,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 🔺 العنوان الرئيسي
              const Text(
                '⚠️ هل تريد إنهاء التوصيل؟',
                style: TextStyle(
                  fontSize: 21,
                  fontWeight: FontWeight.bold,
                  color: Colors.redAccent,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 20),

              // 📋 محتوى منظم
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'يرجى الملاحظة قبل المتابعة:',
                    style: TextStyle(
                      fontSize: 16.5,
                      fontWeight: FontWeight.w700,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(height: 10),

                  Text(
                    '• سيتم تصنيف الطلب كفاشل بشكل دائم.',
                    style: TextStyle(fontSize: 15.5),
                  ),
                  SizedBox(height: 6),

                  Text(
                    '• سيؤثر ذلك على سجل التوصيل والتقييم الخاص بك.',
                    style: TextStyle(fontSize: 15.5),
                  ),
                  SizedBox(height: 6),

                  Text(
                    '• قد يؤدي إلى تقليل أولوية الطلبات المستقبلية أو مراجعة حسابك.',
                    style: TextStyle(fontSize: 15.5),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // ❗ مربع التحذير النهائي
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(14),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.redAccent),
                ),
                child: const Text(
                  '⚠️ هذا الإجراء لا يمكن التراجع عنه.\n\n'
                  'فكر جيداً قبل اتخاذ القرار. لا تستخدمه إلا في الحالات القصوى فقط.',
                  style: TextStyle(
                    fontSize: 15.5,
                    color: Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 28),

              // ✅ الأزرار
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                          fontSize: 15,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _reportIssue(status, reason);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.redAccent,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: const Text(
                        'نعم، إنهاء التوصيل',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

  // Build map section
  Widget _buildMapSection() {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: _restaurantLat != null && _restaurantLng != null && _customerLat != null && _customerLng != null
            ? FlutterMap(
                mapController: _mapController,
                options: MapOptions(
                  initialCenter: LatLng(_restaurantLat!, _restaurantLng!),
                  initialZoom: 12.0,
                  interactionOptions: const InteractionOptions(
                    flags: InteractiveFlag.pinchZoom | InteractiveFlag.drag,
                  ),
                ),
                children: [
                  TileLayer(
                    urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                    userAgentPackageName: 'com.example.drivers_app',
                  ),
                  MarkerLayer(
                    markers: [
                      Marker(
                        point: LatLng(_restaurantLat!, _restaurantLng!),
                        width: 40,
                        height: 40,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.orange[600],
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 3),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.3),
                                blurRadius: 6,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.restaurant,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                      Marker(
                        point: LatLng(_customerLat!, _customerLng!),
                        width: 40,
                        height: 40,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.blue[600],
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 3),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.3),
                                blurRadius: 6,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              )
            : Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Colors.grey[100]!, Colors.grey[200]!],
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.purple[50],
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.location_off,
                        size: 32,
                        color: Colors.purple[400],
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'إحداثيات غير متوفرة',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  // Build order info card
  Widget _buildOrderInfoCard(String orderId, String restaurantName, double total, String customerPhone) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 8,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with order ID
          const SizedBox(height: 12),

          // Restaurant Info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange[600],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.restaurant, color: Colors.white, size: 16),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المطعم',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.orange[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        restaurantName,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // Customer Info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.person, color: Colors.white, size: 16),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'العميل',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.blue[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        customerPhone.isNotEmpty ? customerPhone : 'غير محدد',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
                if (_currentStage == 2 && customerPhone.isNotEmpty)
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.green[500]!, Colors.green[600]!],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.green.withOpacity(0.3),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: ElevatedButton.icon(
                      onPressed: () => _launchPhoneApp(customerPhone),
                      icon: const Icon(Icons.call, size: 18),
                      label: const Text(
                        'اتصال',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        foregroundColor: Colors.white,
                        shadowColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          const SizedBox(height: 8),

          // Financial Info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.green[600],
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: const Icon(Icons.attach_money, color: Colors.white, size: 16),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'تفاصيل مالية',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.green[800],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                _buildFinancialRow('قيمة الطلب', '${total.toStringAsFixed(2)} د.ل', Colors.grey[700]!),
                if (_currentOrder['TotalDeliveryFee'] != null) ...[
                  const SizedBox(height: 8),
                  _buildFinancialRow(
                    'رسوم التوصيل',
                    '${_currentOrder['TotalDeliveryFee'].toStringAsFixed(2)} د.ل',
                    Colors.blue[600]!
                  ),
                ],
                if (_currentOrder['EstimatedDriverProfit'] != null) ...[
                  const SizedBox(height: 6),
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Colors.green[300]!, width: 1),
                      ),
                    ),
                    child: _buildFinancialRow(
                      'ربحك المتوقع',
                      '${_currentOrder['EstimatedDriverProfit'].toStringAsFixed(2)} د.ل',
                      Colors.green[700]!,
                      isHighlighted: true,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 10),
          // Distance information
          if (_restaurantLat != null && _restaurantLng != null && _customerLat != null && _customerLng != null) ...[
            Row(
              children: [
                Icon(Icons.route, color: Colors.purple[600], size: 20),
                const SizedBox(width: 10),
                Text(
                  'مسافة التوصيل: ${_calculateDistance(_restaurantLat!, _restaurantLng!, _customerLat!, _customerLng!).toStringAsFixed(1)} كم',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // Build stage progress
  Widget _buildStageProgress() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Stage 1
              Expanded(
                child: Column(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: _currentStage >= 1 ? Colors.orange[600] : Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.restaurant,
                        color: _currentStage >= 1 ? Colors.white : Colors.grey[600],
                        size: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'الذهاب للمطعم',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: _currentStage >= 1 ? Colors.orange[600] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),

              // Connection line
              Expanded(
                child: Container(
                  height: 2,
                  color: _currentStage >= 2 ? Colors.green[600] : Colors.grey[300],
                ),
              ),

              // Stage 2
              Expanded(
                child: Column(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: _currentStage >= 2 ? Colors.green[600] : Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.person,
                        color: _currentStage >= 2 ? Colors.white : Colors.grey[600],
                        size: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'التوصيل للعميل',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: _currentStage >= 2 ? Colors.green[600] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build action buttons
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          if (_currentStage == 1) ..._buildStage1Buttons(),
          if (_currentStage == 2) ..._buildStage2Buttons(),

          if (_isLoading) ...[
            const SizedBox(height: 12),
            const CircularProgressIndicator(),
          ],
        ],
      ),
    );
  }

  // Stage 1 buttons (Going to Restaurant)
  List<Widget> _buildStage1Buttons() {
    return [
      // Navigate to Restaurant button
      SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: _restaurantLat != null && _restaurantLng != null
              ? () => _navigateToLocation(_restaurantLat!, _restaurantLng!, 'المطعم')
              : null,
          icon: const Icon(Icons.navigation),
          label: const Text('التوجه إلى المطعم'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),

      const SizedBox(height: 8),

      // Reached Restaurant button
      SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: _moveToNextStage,
          icon: const Icon(Icons.check_circle),
          label: const Text('وصلت إلى المطعم'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    ];
  }

  // Stage 2 buttons (Delivering to Customer)
  List<Widget> _buildStage2Buttons() {
    return [
      // Navigate to Customer button
      SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: _customerLat != null && _customerLng != null
              ? () => _navigateToLocation(_customerLat!, _customerLng!, 'العميل')
              : null,
          icon: const Icon(Icons.navigation),
          label: const Text('التوجه إلى العميل'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),

      const SizedBox(height: 16),

      // Delivered to Customer button
      SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: _completeDelivery,
          icon: const Icon(Icons.done_all),
          label: const Text('تم التسليم للعميل'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    ];
  }

  // Helper method for financial row
  Widget _buildFinancialRow(String label, String value, Color color, {bool isHighlighted = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isHighlighted ? 16 : 14,
            fontWeight: isHighlighted ? FontWeight.bold : FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isHighlighted ? 18 : 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
