import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:geolocator/geolocator.dart';
import '../services/api_client.dart';

class DeliveryScreen extends StatefulWidget {
  final Map<String, dynamic> order;

  const DeliveryScreen({Key? key, required this.order}) : super(key: key);

  @override
  _DeliveryScreenState createState() => _DeliveryScreenState();
}

class _DeliveryScreenState extends State<DeliveryScreen> {
  bool _isLoading = false;
  late Map<String, dynamic> _currentOrder;
  GoogleMapController? _mapController;
  int _currentStage = 1; // 1: Going to Restaurant, 2: Delivering to Customer

  // Coordinates
  double? _restaurantLat;
  double? _restaurantLng;
  double? _customerLat;
  double? _customerLng;

  // Driver location
  Position? _driverPosition;
  bool _locationPermissionGranted = false;

  @override
  void initState() {
    super.initState();
    _currentOrder = widget.order;
    _extractCoordinates();
    _checkLocationPermission();
  }

  void _extractCoordinates() {
    print('Full order data: $_currentOrder');

    // Extract restaurant coordinates (Start Points)
    _restaurantLat = _currentOrder['StartPointLatitude']?.toDouble();
    _restaurantLng = _currentOrder['StartPointLongitude']?.toDouble();

    // Extract customer coordinates (End Points)
    _customerLat = _currentOrder['EndPointLatitude']?.toDouble();
    _customerLng = _currentOrder['EndPointLongitude']?.toDouble();

    print('Extracted coordinates:');
    print('Restaurant: $_restaurantLat, $_restaurantLng');
    print('Customer: $_customerLat, $_customerLng');
    print('Raw coordinates from order:');
    print('StartPointLatitude: ${_currentOrder['StartPointLatitude']}');
    print('StartPointLongitude: ${_currentOrder['StartPointLongitude']}');
    print('EndPointLatitude: ${_currentOrder['EndPointLatitude']}');
    print('EndPointLongitude: ${_currentOrder['EndPointLongitude']}');
  }

  // Check and request location permission
  Future<void> _checkLocationPermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      _showLocationServiceDialog();
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        _showPermissionDeniedDialog();
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      _showPermissionDeniedForeverDialog();
      return;
    }

    setState(() {
      _locationPermissionGranted = true;
    });

    _getCurrentLocation();
  }

  // Get current driver location
  Future<void> _getCurrentLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      setState(() {
        _driverPosition = position;
      });
    } catch (e) {
      print('Error getting location: $e');
    }
  }

  // Navigate to Google Maps
  Future<void> _navigateToLocation(double lat, double lng, String destination) async {
    final url = 'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }

  // Call customer
  Future<void> _callCustomer() async {
    final customerPhone = _currentOrder['Customer']?['PhoneNum'] ?? '';
    if (customerPhone.isNotEmpty) {
      final url = 'tel:$customerPhone';
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url));
      }
    }
  }

  // Calculate distance between two points
  double _calculateDistance(double lat1, double lng1, double lat2, double lng2) {
    return Geolocator.distanceBetween(lat1, lng1, lat2, lng2) / 1000; // Convert to kilometers
  }

  // Show location service dialog
  void _showLocationServiceDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('خدمة الموقع مطلوبة'),
        content: const Text('يرجى تفعيل خدمة الموقع من إعدادات الجهاز للمتابعة.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await Geolocator.openLocationSettings();
            },
            child: const Text('فتح الإعدادات'),
          ),
        ],
      ),
    );
  }

  // Show permission denied dialog
  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('إذن الموقع مطلوب'),
        content: const Text('يرجى السماح للتطبيق بالوصول إلى موقعك لحساب المسافات وتحسين تجربة التوصيل.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _checkLocationPermission();
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  // Show permission denied forever dialog
  void _showPermissionDeniedForeverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('إذن الموقع مرفوض'),
        content: const Text('تم رفض إذن الموقع نهائياً. يرجى فتح إعدادات التطبيق والسماح بالوصول إلى الموقع.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await Geolocator.openAppSettings();
            },
            child: const Text('فتح إعدادات التطبيق'),
          ),
        ],
      ),
    );
  }

  // Move to next stage
  void _moveToNextStage() {
    setState(() {
      _currentStage = 2;
    });
  }

  // Complete delivery
  Future<void> _completeDelivery() async {
    await _updateOrderStatus(3);
  }

  // Report delivery issue
  Future<void> _reportIssue(int status, String reason) async {
    await _updateOrderStatus(status);
  }

  Future<void> _updateOrderStatus(int status) async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final apiClient = ApiClient();
      final response = await apiClient.put(
        '/drivers/app/delivery-status/${_currentOrder['OrderID']}',
        {'status': status},
      );

      if (response['success'] && mounted) {
        setState(() {
          _currentOrder['Status'] = status;
        });

        String message = '';
        switch (status) {
          case 3:
            message = 'تم تسليم الطلب بنجاح';
            Navigator.of(context).popUntil((route) => route.isFirst);
            break;
          case 5:
            message = 'تم تسجيل عدم استلام العميل للطلب';
            Navigator.of(context).popUntil((route) => route.isFirst);
            break;
          case 7:
            message = 'تم إنهاء الرحلة بسبب فشل التوصيل';
            Navigator.of(context).popUntil((route) => route.isFirst);
            break;
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('حدث خطأ أثناء تحديث حالة الطلب'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final orderId = _currentOrder['OrderID']?.toString() ?? 'N/A';
    final restaurantName = _currentOrder['RestaurantName'] ?? 'مطعم';
    final total = _currentOrder['CartTotalPrice']?.toDouble() ?? 0.0;
    final customerPhone = _currentOrder['Customer']?['PhoneNum'] ?? '';

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text('توصيل الطلب #$orderId'),
        backgroundColor: Colors.purple[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: _handleMenuSelection,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'report',
                child: Text('بلاغ'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Map Section
          _buildMapSection(),

          // Order Info Card
          _buildOrderInfoCard(orderId, restaurantName, total, customerPhone),

          // Stage Progress
          _buildStageProgress(),

          // Action Buttons
          Expanded(
            child: _buildActionButtons(),
          ),
        ],
      ),
    );
  }

  void _handleMenuSelection(String value) {
    if (value == 'report') {
      _showReportMenu();
    }
  }

  void _showReportMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'بلاغ',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.cancel, color: Colors.red),
              title: const Text('إنهاء الرحلة بسبب الفشل في التوصيل'),
              onTap: () {
                Navigator.pop(context);
                _showWarningModal(7, 'فشل التوصيل');
              },
            ),
            ListTile(
              leading: const Icon(Icons.person_off, color: Colors.orange),
              title: const Text('إنهاء الرحلة بسبب عدم استلام الزبون'),
              onTap: () {
                Navigator.pop(context);
                _showWarningModal(5, 'عدم استلام الزبون');
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // Warning modal for reports
  void _showWarningModal(int status, String reason) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.red[50],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.red[600], size: 28),
            const SizedBox(width: 10),
            const Text(
              '⚠️ تحذير!',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'هل أنت متأكد من إنهاء هذا التوصيل؟',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 15),
            const Text('هذا الإجراء سوف:'),
            const SizedBox(height: 8),
            const Text('• يضع علامة دائمة على الطلب كفاشل'),
            const Text('• يؤثر على سجل التوصيل والتقييم الخاص بك'),
            const Text('• قد يؤدي إلى مراجعة أو إجراء ضد حسابك'),
            const SizedBox(height: 15),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red[300]!),
              ),
              child: const Text(
                '⚠️ هذا الإجراء لا يمكن التراجع عنه.\n\nفكر جيداً قبل المتابعة.',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _reportIssue(status, reason);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('نعم، إنهاء التوصيل'),
          ),
        ],
      ),
    );
  }

  // Build map section
  Widget _buildMapSection() {
    return Container(
      height: 200,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15),
        child: Container(
          color: Colors.grey[100],
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.purple[50],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.map,
                  size: 40,
                  color: Colors.purple[600],
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'خريطة التوصيل',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 8),
              if (_restaurantLat != null && _customerLat != null)
                Text(
                  'المطعم → العميل',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                )
              else
                Text(
                  'إحداثيات غير متوفرة',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Build order info card
  Widget _buildOrderInfoCard(String orderId, String restaurantName, double total, String customerPhone) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.purple[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'طلب #$orderId',
                  style: TextStyle(
                    color: Colors.purple[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Row(
            children: [
              Icon(Icons.restaurant, color: Colors.purple[600], size: 20),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                  restaurantName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            children: [
              Icon(Icons.phone, color: Colors.purple[600], size: 20),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                  customerPhone,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
              if (_currentStage == 2 && customerPhone.isNotEmpty)
                ElevatedButton.icon(
                  onPressed: _callCustomer,
                  icon: const Icon(Icons.call, size: 16),
                  label: const Text('اتصال'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    minimumSize: const Size(0, 0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            children: [
              Icon(Icons.attach_money, color: Colors.purple[600], size: 20),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إجمالي الطلب: ${total.toStringAsFixed(2)} د.ل',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.green,
                      ),
                    ),
                    if (_currentOrder['TotalDeliveryFee'] != null)
                      Text(
                        'رسوم التوصيل: ${_currentOrder['TotalDeliveryFee'].toStringAsFixed(2)} د.ل',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    if (_currentOrder['EstimatedDriverProfit'] != null)
                      Text(
                        'ربحك المتوقع: ${_currentOrder['EstimatedDriverProfit'].toStringAsFixed(2)} د.ل',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.green[700],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          // Distance information
          if (_locationPermissionGranted && _driverPosition != null) ...[
            if (_restaurantLat != null && _restaurantLng != null)
              Row(
                children: [
                  Icon(Icons.route, color: Colors.purple[600], size: 20),
                  const SizedBox(width: 10),
                  Text(
                    'المسافة للمطعم: ${_calculateDistance(_driverPosition!.latitude, _driverPosition!.longitude, _restaurantLat!, _restaurantLng!).toStringAsFixed(1)} كم',
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
            const SizedBox(height: 5),
            if (_customerLat != null && _customerLng != null)
              Row(
                children: [
                  Icon(Icons.location_on, color: Colors.purple[600], size: 20),
                  const SizedBox(width: 10),
                  Text(
                    'المسافة للعميل: ${_calculateDistance(_driverPosition!.latitude, _driverPosition!.longitude, _customerLat!, _customerLng!).toStringAsFixed(1)} كم',
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
          ],
        ],
      ),
    );
  }

  // Build stage progress
  Widget _buildStageProgress() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Stage 1
              Expanded(
                child: Column(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: _currentStage >= 1 ? Colors.orange[600] : Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.restaurant,
                        color: _currentStage >= 1 ? Colors.white : Colors.grey[600],
                        size: 20,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'الذهاب للمطعم',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: _currentStage >= 1 ? Colors.orange[600] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),

              // Connection line
              Expanded(
                child: Container(
                  height: 2,
                  color: _currentStage >= 2 ? Colors.green[600] : Colors.grey[300],
                ),
              ),

              // Stage 2
              Expanded(
                child: Column(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: _currentStage >= 2 ? Colors.green[600] : Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.person,
                        color: _currentStage >= 2 ? Colors.white : Colors.grey[600],
                        size: 20,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'التوصيل للعميل',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: _currentStage >= 2 ? Colors.green[600] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build action buttons
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (_currentStage == 1) ..._buildStage1Buttons(),
          if (_currentStage == 2) ..._buildStage2Buttons(),

          if (_isLoading) ...[
            const SizedBox(height: 20),
            const CircularProgressIndicator(),
          ],
        ],
      ),
    );
  }

  // Stage 1 buttons (Going to Restaurant)
  List<Widget> _buildStage1Buttons() {
    return [
      // Navigate to Restaurant button
      SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: _restaurantLat != null && _restaurantLng != null
              ? () => _navigateToLocation(_restaurantLat!, _restaurantLng!, 'المطعم')
              : null,
          icon: const Icon(Icons.navigation),
          label: const Text('التوجه إلى المطعم'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),

      const SizedBox(height: 16),

      // Reached Restaurant button
      SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: _moveToNextStage,
          icon: const Icon(Icons.check_circle),
          label: const Text('وصلت إلى المطعم'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    ];
  }

  // Stage 2 buttons (Delivering to Customer)
  List<Widget> _buildStage2Buttons() {
    return [
      // Navigate to Customer button
      SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: _customerLat != null && _customerLng != null
              ? () => _navigateToLocation(_customerLat!, _customerLng!, 'العميل')
              : null,
          icon: const Icon(Icons.navigation),
          label: const Text('التوجه إلى العميل'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),

      const SizedBox(height: 16),

      // Delivered to Customer button
      SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: _completeDelivery,
          icon: const Icon(Icons.done_all),
          label: const Text('تم التسليم للعميل'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    ];
  }
}
