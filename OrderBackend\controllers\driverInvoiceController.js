const sequelize = require("../config/database");
const models = require('../models/init-models')(sequelize);
const { DriverInvoices , Driver, Order , UserAccount } = models;
const { getPagination, getPagingData } = require('../utils/pagination');

exports.createDriverInvoice = async (req, res) => {
    try {
        const { DriverID, Price, ReceivedBy } = req.body;

        const driver = await Driver.findByPk(DriverID);
        if (!driver) {
            return res.status(404).json({ message: 'Driver not found' });
        }

        if (driver.Wallet < Price) {
            return res.status(400).json({
                message: 'Insufficient wallet balance',
                currentBalance: driver.Wallet,
                requiredAmount: Price
            });
        }

        const driverInvoice = await DriverInvoices.create({
            DriverID,
            Date: new Date(),
            Price,
            ReceivedBy
        });

        const newWalletAmount = driver.Wallet - Price;
        await driver.update({
            Wallet: newWalletAmount,
            LastWalletUpdate: new Date()
        });

        res.status(201).json({
            message: 'Driver invoice created successfully',
            driverInvoice,
            newWalletBalance: newWalletAmount
        });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};
exports.getAllDriverInvoices = async (req, res) => {
    try {
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        const driverInvoices = await DriverInvoices.findAndCountAll({
            limit,
            offset,
            include: [{
                model: Driver,
                as: 'Driver',
                attributes: [
                    [sequelize.fn('CONCAT', sequelize.col('FirstName'), ' ', sequelize.col('LastName')), 'Name']
                ]
            }],
            order: [['Date', 'DESC']]
        });

        const receivedByPromises = driverInvoices.rows.map(async invoice => {
            const user = await UserAccount.findOne({
                where: { UserID: invoice.ReceivedBy },
                attributes: ['Username']
            });
            return { ...invoice.dataValues, ReceivedByUsername: user ? user.Username : 'Unknown' };
        });

        const invoicesWithUsernames = await Promise.all(receivedByPromises);

        const response = getPagingData({ ...driverInvoices, rows: invoicesWithUsernames }, page, limit);

        res.status(200).json(response);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};


exports.getDriverInvoicesByDriverId = async (req, res) => {
    try {
        const { driverId } = req.params;
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        const driverInvoices = await DriverInvoices.findAndCountAll({
            where: { DriverID: driverId },
            limit,
            offset,
            include: [{
                model: Driver,
                as: 'DriverInvoices',
                attributes: [
                    [sequelize.fn('CONCAT', sequelize.col('FirstName'), ' ', sequelize.col('LastName')), 'Name']
                ]
            }],
            order: [['Date', 'DESC']]
        });

        const response = getPagingData(driverInvoices, page, limit);
        res.status(200).json(response);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.getNotReceivedWallets = async (req, res) => {
    try {
        const result = await Driver.sum('Wallet', {
            where: {
                Wallet: {
                    [Op.gt]: 0
                }
            }
        });

        res.status(200).json({
            totalNotReceivedAmount: parseFloat(result || 0)
        });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.getNotReceivedDrivers = async (req, res) => {
    try {
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        const drivers = await Driver.findAndCountAll({
            where: {
                Wallet: {
                    [Op.gt]: 0
                }
            },
            attributes: ['DriverID', 'Wallet', 'LastWalletUpdate'],
            include: [{
                model: Driver,
                as: 'Driver',
                attributes: [
                    [sequelize.fn('CONCAT', sequelize.col('FirstName'), ' ', sequelize.col('LastName')), 'Name']
                ]
            }],
            limit,
            offset,
            order: [['Wallet', 'DESC']]
        });

        const response = getPagingData(drivers, page, limit);
        res.status(200).json(response);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};
