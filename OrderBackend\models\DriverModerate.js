const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('DriverModerate', {
    News: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    NewsExpireDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    CompanyProfitPercent: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    UpdateBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'UserAccount',
        key: 'UserID'
      }
    },
    ResturantProfitPercent: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    IsConst: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    ConstValue: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    ID: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    CostPerKilometer: {
      type: DataTypes.DECIMAL(5,2),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'DriverModerate',
    schema: 'dbo',
    timestamps: false,
    indexes: [
      {
        name: "PK__DriverMo__3214EC27DA62D450",
        unique: true,
        fields: [
          { name: "ID" },
        ]
      },
    ]
  });
};
