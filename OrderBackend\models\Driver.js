const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Driver', {
    DriverID: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    FirstName: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    LastName: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    IsWorking: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    JoinDate: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    Username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: "UQ__Driver__536C85E439A31A7D"
    },
    PasswordHash: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    CurrentOrderID: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    Status: {
      type: DataTypes.TINYINT,
      allowNull: false
    },
    Wallet: {
      type: DataTypes.DECIMAL(10,2),
      allowNull: false,
      defaultValue: 0.00
    },
    LastWalletUpdate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    PhoneNumber: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    Address: {
      type: DataTypes.STRING(250),
      allowNull: true
    },
    CompanyCar: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    Activated: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    licenseImage: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    CarData: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    PlateNumber: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    Profit: {
      type: DataTypes.DECIMAL(10,2),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'Driver',
    schema: 'dbo',
    timestamps: false,
    indexes: [
      {
        name: "PK__Driver__F1B1CD2476902D45",
        unique: true,
        fields: [
          { name: "DriverID" },
        ]
      },
      {
        name: "UQ__Driver__536C85E439A31A7D",
        unique: true,
        fields: [
          { name: "Username" },
        ]
      },
    ]
  });
};
