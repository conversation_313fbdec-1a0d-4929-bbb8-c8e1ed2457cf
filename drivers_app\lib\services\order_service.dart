import 'dart:convert';

import '../models/order.dart';
import '../services/api_client.dart';
import '../providers/auth_provider.dart';

class OrderService {
  final ApiClient _apiClient = ApiClient();
  
  // Get all orders assigned to the driver
  Future<List<Order>> getDriverOrders({required int driverId}) async {
    try {
      final response = await _apiClient.get('/orders/driver/$driverId');
      
      if (response is List) {
        return response
            .map<Order>((orderJson) => Order.fromJson(orderJson))
            .toList();
      }
      
      return [];
    } catch (e) {
      print('Error fetching driver orders: $e');
      return [];
    }
  }
  
  // Get active orders (pending, accepted, delivering)
  Future<List<Order>> getActiveOrders({required int driverId}) async {
    try {
      final allOrders = await getDriverOrders(driverId: driverId);
      return allOrders.where((order) => order.isActive).toList();
    } catch (e) {
      print('Error fetching active orders: $e');
      return [];
    }
  }
  
  // Get completed orders (delivered, failed, etc.)
  Future<List<Order>> getCompletedOrders({required int driverId}) async {
    try {
      final allOrders = await getDriverOrders(driverId: driverId);
      return allOrders.where((order) => order.isCompleted).toList();
    } catch (e) {
      print('Error fetching completed orders: $e');
      return [];
    }
  }
  
  // Get detailed information about an order
  Future<Order?> getOrderDetails({required int orderId}) async {
    try {
      final response = await _apiClient.get('/orders/details/$orderId');
      return Order.fromJson(response);
    } catch (e) {
      print('Error fetching order details: $e');
      return null;
    }
  }
  
  // Update order status
  Future<bool> updateOrderStatus({
    required int orderId, 
    required int newStatus
  }) async {
    try {
      await _apiClient.put('/orders/$orderId', {
        'Status': newStatus
      });
      return true;
    } catch (e) {
      print('Error updating order status: $e');
      return false;
    }
  }
  
  // Get available orders that have no driver assigned
  Future<List<Order>> getAvailableOrders() async {
    try {
      final response = await _apiClient.get('/orders/available');
      
      if (response is List) {
        return response
            .map<Order>((orderJson) => Order.fromJson(orderJson))
            .toList();
      }
      
      return [];
    } catch (e) {
      print('Error fetching available orders: $e');
      return [];
    }
  }
  
  // Accept an order (assign to this driver)
  Future<bool> acceptOrder({
    required int orderId, 
    required int driverId
  }) async {
    try {
      await _apiClient.put('/orders/$orderId', {
        'DriverID': driverId,
        'Status': 1 // Accepted status
      });
      return true;
    } catch (e) {
      print('Error accepting order: $e');
      return false;
    }
  }
} 